c0 = 343;
c1 = Function[a, 343*(1 + a*I)];(*loss*)
c2 = Function[a, 343*(1 - a*I)];(*gain*)
k = Function[{f, c}, 2*Pi*f/c];
lossT = Function[{f, L, 
    a}, {{Cos[k[f, c1[a]]*L], I*Sin[k[f, c1[a]]*L]*c1[a]},
    {I*Sin[k[f, c1[a]]*L]/c1[a], Cos[k[f, c1[a]]*L]}}];
gainT = Function[{f, L, 
    a}, {{Cos[k[f, c2[a]]*L], I*Sin[k[f, c2[a]]*L]*c2[a]},
    {I*Sin[k[f, c2[a]]*L]/c2[a], Cos[k[f, c2[a]]*L]}}];
(*下面系统为单loss+单gain*)
bigT1 = Function[{f, L, Lmid, a},
   lossT[f, L, a] . gainT[f, L, a]];
(*下面为透射*)
bigT1tsm = 
  Function[{f, L, Lmid, a}, 
   Power[2/Abs[
      bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 +
        bigT1[f, L, Lmid, a][[2, 1]]*c0 + 
       bigT1[f, L, Lmid, a][[2, 2]]], 2]];
(*下面为左侧的反射*)
bigT1leftR = 
  Function[{f, L, Lmid, a}, 
   Power[Abs[(bigT1[f, L, Lmid, a][[1, 1]] + 
        bigT1[f, L, Lmid, a][[1, 2]]/c0 - 
        bigT1[f, L, Lmid, a][[2, 1]]*c0 - 
        bigT1[f, L, Lmid, a][[2, 2]])/(bigT1[f, L, Lmid, a][[1, 1]] + 
        bigT1[f, L, Lmid, a][[1, 2]]/c0 + 
        bigT1[f, L, Lmid, a][[2, 1]]*c0 + 
        bigT1[f, L, Lmid, a][[2, 2]])], 2]];
(*下面为右侧的反射*)
bigT1rightR = 
  Function[{f, L, Lmid, a}, 
   Power[Abs[(-bigT1[f, L, Lmid, a][[1, 1]] + 
        bigT1[f, L, Lmid, a][[1, 2]]/c0 - 
        bigT1[f, L, Lmid, a][[2, 1]]*c0 + 
        bigT1[f, L, Lmid, a][[2, 2]])/(bigT1[f, L, Lmid, a][[1, 1]] + 
        bigT1[f, L, Lmid, a][[1, 2]]/c0 + 
        bigT1[f, L, Lmid, a][[2, 1]]*c0 + 
        bigT1[f, L, Lmid, a][[2, 2]])], 2]];