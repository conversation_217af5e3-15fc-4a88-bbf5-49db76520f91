(* 简单测试代码 *)
Print["开始测试..."];

(* 基本参数 *)
c0 = 343;
c1 = Function[a, 343*(1 + a*I)];
c2 = Function[a, 343*(1 - a*I)];
k = Function[{f, c}, 2*Pi*f/c];

Print["参数定义完成"];

(* 传输矩阵 *)
lossT = Function[{f, L, a}, 
  {{Cos[k[f, c1[a]]*L], I*Sin[k[f, c1[a]]*L]*c1[a]},
   {I*Sin[k[f, c1[a]]*L]/c1[a], Cos[k[f, c1[a]]*L]}}];

gainT = Function[{f, L, a}, 
  {{Cos[k[f, c2[a]]*L], I*Sin[k[f, c2[a]]*L]*c2[a]},
   {I*Sin[k[f, c2[a]]*L]/c2[a], Cos[k[f, c2[a]]*L]}}];

bigT1 = Function[{f, L, Lmid, a}, lossT[f, L, a].gainT[f, L, a]];

Print["传输矩阵定义完成"];

(* 透射系数 *)
transmission = Function[{f, L, Lmid, a}, 
  Power[2/Abs[bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 + 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 + bigT1[f, L, Lmid, a][[2, 2]]], 2]];

Print["透射系数定义完成"];

(* 测试单点计算 *)
testResult = transmission[100, 0.1, 0.1, 0.01];
Print["测试计算结果: ", testResult];

(* 创建简单的二维图 *)
Print["开始创建图像..."];
plot = DensityPlot[
  transmission[f, 0.1, 0.1, a],
  {f, 10, 1000},
  {a, 0.001, 0.1},
  PlotLabel -> "透射系数测试",
  FrameLabel -> {"频率 (Hz)", "声速虚部系数"},
  PlotPoints -> 30,
  ImageSize -> 400
];

Print["图像创建完成，正在导出..."];
Export["测试图像.png", plot];
Print["图像导出完成！"];

(* 检查文件是否存在 *)
If[FileExistsQ["测试图像.png"], 
  Print["文件成功创建: 测试图像.png"], 
  Print["文件创建失败"]];
