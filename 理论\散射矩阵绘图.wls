(* 双端口声学波导系统散射矩阵绘图 *)
(* 包含损耗段和增益段的波导系统 *)

(* 基本参数定义 *)
c0 = 343; (* 参考声速 *)

(* 复声速定义 *)
(* c1: 损耗段声速，具有正虚部 *)
c1 = Function[a, 343*(1 + a*I)];
(* c2: 增益段声速，具有负虚部 *)
c2 = Function[a, 343*(1 - a*I)];

(* 波数函数 *)
k = Function[{f, c}, 2*Pi*f/c];

(* 传输矩阵定义 *)
(* 损耗段传输矩阵 *)
lossT = Function[{f, L, a}, 
  {{Cos[k[f, c1[a]]*L], I*Sin[k[f, c1[a]]*L]*c1[a]},
   {I*Sin[k[f, c1[a]]*L]/c1[a], Cos[k[f, c1[a]]*L]}}];

(* 增益段传输矩阵 *)
gainT = Function[{f, L, a}, 
  {{Cos[k[f, c2[a]]*L], I*Sin[k[f, c2[a]]*L]*c2[a]},
   {I*Sin[k[f, c2[a]]*L]/c2[a], Cos[k[f, c2[a]]*L]}}];

(* 总传输矩阵：损耗段 + 增益段 *)
bigT1 = Function[{f, L, Lmid, a}, lossT[f, L, a].gainT[f, L, a]];

(* 散射矩阵元素计算 *)
(* 透射系数 *)
transmission = Function[{f, L, Lmid, a}, 
  Power[2/Abs[bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 + 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 + bigT1[f, L, Lmid, a][[2, 2]]], 2]];

(* 左侧反射系数 *)
leftReflection = Function[{f, L, Lmid, a}, 
  Power[Abs[(bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 - 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 - bigT1[f, L, Lmid, a][[2, 2]])/
    (bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 + 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 + bigT1[f, L, Lmid, a][[2, 2]])], 2]];

(* 右侧反射系数 *)
rightReflection = Function[{f, L, Lmid, a}, 
  Power[Abs[(-bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 - 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 + bigT1[f, L, Lmid, a][[2, 2]])/
    (bigT1[f, L, Lmid, a][[1, 1]] + bigT1[f, L, Lmid, a][[1, 2]]/c0 + 
    bigT1[f, L, Lmid, a][[2, 1]]*c0 + bigT1[f, L, Lmid, a][[2, 2]])], 2]];

(* 绘图参数设置 *)
L = 0.1; (* 损耗段长度，单位：米 *)
Lmid = 0.1; (* 增益段长度，单位：米 *)
fRange = {1, 1000}; (* 频率范围，单位：Hz *)
aRange = {0.001, 0.1}; (* 声速虚部系数范围 *)

(* 创建二维绘图 *)
Print["正在生成透射系数二维图像..."];
transmissionPlot = DensityPlot[
  transmission[f, L, Lmid, a],
  {f, fRange[[1]], fRange[[2]]},
  {a, aRange[[1]], aRange[[2]]},
  PlotLabel -> "透射系数 |T|²",
  FrameLabel -> {"频率 (Hz)", "声速虚部系数"},
  ColorFunction -> "Rainbow",
  PlotLegends -> Automatic,
  ImageSize -> 500
];

Print["正在生成左侧反射系数二维图像..."];
leftReflectionPlot = DensityPlot[
  leftReflection[f, L, Lmid, a],
  {f, fRange[[1]], fRange[[2]]},
  {a, aRange[[1]], aRange[[2]]},
  PlotLabel -> "左侧反射系数 |R_L|²",
  FrameLabel -> {"频率 (Hz)", "声速虚部系数"},
  ColorFunction -> "Rainbow",
  PlotLegends -> Automatic,
  ImageSize -> 500
];

Print["正在生成右侧反射系数二维图像..."];
rightReflectionPlot = DensityPlot[
  rightReflection[f, L, Lmid, a],
  {f, fRange[[1]], fRange[[2]]},
  {a, aRange[[1]], aRange[[2]]},
  PlotLabel -> "右侧反射系数 |R_R|²",
  FrameLabel -> {"频率 (Hz)", "声速虚部系数"},
  ColorFunction -> "Rainbow",
  PlotLegends -> Automatic,
  ImageSize -> 500
];

(* 导出图像到文件 *)
Print["导出透射系数图像..."];
Export["透射系数.png", transmissionPlot];

Print["导出左侧反射系数图像..."];
Export["左侧反射系数.png", leftReflectionPlot];

Print["导出右侧反射系数图像..."];
Export["右侧反射系数.png", rightReflectionPlot];

(* 组合图像 *)
combinedPlot = GraphicsGrid[{{transmissionPlot}, {leftReflectionPlot}, {rightReflectionPlot}},
  ImageSize -> 800, Spacings -> {10, 10}];

Print["导出组合图像..."];
Export["散射矩阵组合图.png", combinedPlot];

Print["所有图像已成功导出！"];
Print["文件列表："];
Print["- 透射系数.png"];
Print["- 左侧反射系数.png"];
Print["- 右侧反射系数.png"];
Print["- 散射矩阵组合图.png"];
