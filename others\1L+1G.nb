(* Content-type: application/vnd.wolfram.mathematica *)

(*** Wolfram Notebook File ***)
(* http://www.wolfram.com/nb *)

(* CreatedBy='Mathematica 13.2' *)

(*CacheID: 234*)
(* Internal cache information:
NotebookFileLineBreakTest
NotebookFileLineBreakTest
NotebookDataPosition[       158,          7]
NotebookDataLength[     15853,        382]
NotebookOptionsPosition[     15434,        366]
NotebookOutlinePosition[     15898,        384]
CellTagsIndexPosition[     15855,        381]
WindowFrame->Normal*)

(* Beginning of Notebook Content *)
Notebook[{
Cell[BoxData[
 RowBox[{
  RowBox[{"(*", "\:4ee5\:4e0b\:4e3a\:6b63\:5f0f\:7a0b\:5e8f", "*)"}], 
  "\[IndentingNewLine]", 
  RowBox[{
   RowBox[{
    RowBox[{"c0", "=", "343"}], ";"}], "\[IndentingNewLine]", 
   RowBox[{
    RowBox[{"c1", "=", 
     RowBox[{"Function", "[", 
      RowBox[{"x", ",", 
       RowBox[{"343", "*", 
        RowBox[{"(", 
         RowBox[{"1", "+", 
          RowBox[{"x", "*", "I"}]}], ")"}]}]}], "]"}]}], ";"}], 
   RowBox[{"(*", "loss", "*)"}], "\[IndentingNewLine]", 
   RowBox[{
    RowBox[{"c2", "=", 
     RowBox[{"Function", "[", 
      RowBox[{"x", ",", 
       RowBox[{"343", "*", 
        RowBox[{"(", 
         RowBox[{"1", "-", 
          RowBox[{"x", "*", "I"}]}], ")"}]}]}], "]"}]}], ";"}], 
   RowBox[{"(*", "gain", "*)"}], "\[IndentingNewLine]", 
   RowBox[{
    RowBox[{"k", "=", 
     RowBox[{"Function", "[", 
      RowBox[{
       RowBox[{"{", 
        RowBox[{"f", ",", "c"}], "}"}], ",", 
       RowBox[{"2", "*", "Pi", "*", 
        RowBox[{"f", "/", "c"}]}]}], "]"}]}], ";"}], "\[IndentingNewLine]", 
   RowBox[{
    RowBox[{"lossT", "=", 
     RowBox[{"Function", "[", 
      RowBox[{
       RowBox[{"{", 
        RowBox[{"f", ",", "L", ",", "x"}], "}"}], ",", 
       RowBox[{"{", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"Cos", "[", 
            RowBox[{
             RowBox[{"k", "[", 
              RowBox[{"f", ",", 
               RowBox[{"c1", "[", "x", "]"}]}], "]"}], "*", "L"}], "]"}], ",", 
           RowBox[{"I", "*", 
            RowBox[{"Sin", "[", 
             RowBox[{
              RowBox[{"k", "[", 
               RowBox[{"f", ",", 
                RowBox[{"c1", "[", "x", "]"}]}], "]"}], "*", "L"}], "]"}], 
            "*", 
            RowBox[{"c1", "[", "x", "]"}]}]}], "}"}], ",", 
         "\[IndentingNewLine]", 
         RowBox[{"{", 
          RowBox[{
           RowBox[{"I", "*", 
            RowBox[{
             RowBox[{"Sin", "[", 
              RowBox[{
               RowBox[{"k", "[", 
                RowBox[{"f", ",", 
                 RowBox[{"c1", "[", "x", "]"}]}], "]"}], "*", "L"}], "]"}], 
             "/", 
             RowBox[{"c1", "[", "x", "]"}]}]}], ",", 
           RowBox[{"Cos", "[", 
            RowBox[{
             RowBox[{"k", "[", 
              RowBox[{"f", ",", 
               RowBox[{"c1", "[", "x", "]"}]}], "]"}], "*", "L"}], "]"}]}], 
          "}"}]}], "}"}]}], "]"}]}], ";"}], "\[IndentingNewLine]", 
   RowBox[{
    RowBox[{"gainT", "=", 
     RowBox[{"Function", "[", 
      RowBox[{
       RowBox[{"{", 
        RowBox[{"f", ",", "L", ",", "x"}], "}"}], ",", 
       RowBox[{"{", 
        RowBox[{
         RowBox[{"{", 
          RowBox[{
           RowBox[{"Cos", "[", 
            RowBox[{
             RowBox[{"k", "[", 
              RowBox[{"f", ",", 
               RowBox[{"c2", "[", "x", "]"}]}], "]"}], "*", "L"}], "]"}], ",", 
           RowBox[{"I", "*", 
            RowBox[{"Sin", "[", 
             RowBox[{
              RowBox[{"k", "[", 
               RowBox[{"f", ",", 
                RowBox[{"c2", "[", "x", "]"}]}], "]"}], "*", "L"}], "]"}], 
            "*", 
            RowBox[{"c2", "[", "x", "]"}]}]}], "}"}], ",", 
         "\[IndentingNewLine]", 
         RowBox[{"{", 
          RowBox[{
           RowBox[{"I", "*", 
            RowBox[{
             RowBox[{"Sin", "[", 
              RowBox[{
               RowBox[{"k", "[", 
                RowBox[{"f", ",", 
                 RowBox[{"c2", "[", "x", "]"}]}], "]"}], "*", "L"}], "]"}], 
             "/", 
             RowBox[{"c2", "[", "x", "]"}]}]}], ",", 
           RowBox[{"Cos", "[", 
            RowBox[{
             RowBox[{"k", "[", 
              RowBox[{"f", ",", 
               RowBox[{"c2", "[", "x", "]"}]}], "]"}], "*", "L"}], "]"}]}], 
          "}"}]}], "}"}]}], "]"}]}], ";"}], "\[IndentingNewLine]", 
   RowBox[{"(*", 
    RowBox[{"\:4e0b\:9762\:7cfb\:7edf\:4e3a\:5355loss", "+", "\:5355gain"}], 
    "*)"}], "\[IndentingNewLine]", 
   RowBox[{
    RowBox[{"bigT1", "=", 
     RowBox[{"Function", "[", 
      RowBox[{
       RowBox[{"{", 
        RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "}"}], ",", 
       "\[IndentingNewLine]", 
       RowBox[{
        RowBox[{"lossT", "[", 
         RowBox[{"f", ",", "L", ",", "x"}], "]"}], ".", 
        RowBox[{"gainT", "[", 
         RowBox[{"f", ",", "L", ",", "x"}], "]"}]}]}], "]"}]}], ";"}], 
   "\[IndentingNewLine]", 
   RowBox[{"(*", "\:4e0b\:9762\:4e3a\:900f\:5c04", "*)"}], 
   "\[IndentingNewLine]", 
   RowBox[{
    RowBox[{"bigT1tsm", "=", 
     RowBox[{"Function", "[", 
      RowBox[{
       RowBox[{"{", 
        RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "}"}], ",", 
       RowBox[{"Power", "[", 
        RowBox[{
         RowBox[{"2", "/", 
          RowBox[{"Abs", "[", 
           RowBox[{
            RowBox[{
             RowBox[{"bigT1", "[", 
              RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
             RowBox[{"[", 
              RowBox[{"1", ",", "1"}], "]"}], "]"}], "+", 
            RowBox[{
             RowBox[{
              RowBox[{"bigT1", "[", 
               RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
              RowBox[{"[", 
               RowBox[{"1", ",", "2"}], "]"}], "]"}], "/", "c0"}], "+", 
            RowBox[{
             RowBox[{
              RowBox[{"bigT1", "[", 
               RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
              RowBox[{"[", 
               RowBox[{"2", ",", "1"}], "]"}], "]"}], "*", "c0"}], "+", 
            RowBox[{
             RowBox[{"bigT1", "[", 
              RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
             RowBox[{"[", 
              RowBox[{"2", ",", "2"}], "]"}], "]"}]}], "]"}]}], ",", "2"}], 
        "]"}]}], "]"}]}], ";"}], "\[IndentingNewLine]", 
   RowBox[{"(*", "\:4e0b\:9762\:4e3a\:5de6\:4fa7\:7684\:53cd\:5c04", "*)"}], 
   "\[IndentingNewLine]", 
   RowBox[{
    RowBox[{"bigT1leftR", "=", 
     RowBox[{"Function", "[", 
      RowBox[{
       RowBox[{"{", 
        RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "}"}], ",", 
       RowBox[{"Power", "[", 
        RowBox[{
         RowBox[{"Abs", "[", 
          RowBox[{
           RowBox[{"(", 
            RowBox[{
             RowBox[{
              RowBox[{"bigT1", "[", 
               RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
              RowBox[{"[", 
               RowBox[{"1", ",", "1"}], "]"}], "]"}], "+", 
             RowBox[{
              RowBox[{
               RowBox[{"bigT1", "[", 
                RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
               RowBox[{"[", 
                RowBox[{"1", ",", "2"}], "]"}], "]"}], "/", "c0"}], "-", 
             RowBox[{
              RowBox[{
               RowBox[{"bigT1", "[", 
                RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
               RowBox[{"[", 
                RowBox[{"2", ",", "1"}], "]"}], "]"}], "*", "c0"}], "-", 
             RowBox[{
              RowBox[{"bigT1", "[", 
               RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
              RowBox[{"[", 
               RowBox[{"2", ",", "2"}], "]"}], "]"}]}], ")"}], "/", 
           RowBox[{"(", 
            RowBox[{
             RowBox[{
              RowBox[{"bigT1", "[", 
               RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
              RowBox[{"[", 
               RowBox[{"1", ",", "1"}], "]"}], "]"}], "+", 
             RowBox[{
              RowBox[{
               RowBox[{"bigT1", "[", 
                RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
               RowBox[{"[", 
                RowBox[{"1", ",", "2"}], "]"}], "]"}], "/", "c0"}], "+", 
             RowBox[{
              RowBox[{
               RowBox[{"bigT1", "[", 
                RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
               RowBox[{"[", 
                RowBox[{"2", ",", "1"}], "]"}], "]"}], "*", "c0"}], "+", 
             RowBox[{
              RowBox[{"bigT1", "[", 
               RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
              RowBox[{"[", 
               RowBox[{"2", ",", "2"}], "]"}], "]"}]}], ")"}]}], "]"}], ",", 
         "2"}], "]"}]}], "]"}]}], ";"}], "\[IndentingNewLine]", 
   RowBox[{"(*", "\:4e0b\:9762\:4e3a\:53f3\:4fa7\:7684\:53cd\:5c04", "*)"}], 
   "\[IndentingNewLine]", 
   RowBox[{
    RowBox[{"bigT1rightR", "=", 
     RowBox[{"Function", "[", 
      RowBox[{
       RowBox[{"{", 
        RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "}"}], ",", 
       RowBox[{"Power", "[", 
        RowBox[{
         RowBox[{"Abs", "[", 
          RowBox[{
           RowBox[{"(", 
            RowBox[{
             RowBox[{"-", 
              RowBox[{
               RowBox[{"bigT1", "[", 
                RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
               RowBox[{"[", 
                RowBox[{"1", ",", "1"}], "]"}], "]"}]}], "+", 
             RowBox[{
              RowBox[{
               RowBox[{"bigT1", "[", 
                RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
               RowBox[{"[", 
                RowBox[{"1", ",", "2"}], "]"}], "]"}], "/", "c0"}], "-", 
             RowBox[{
              RowBox[{
               RowBox[{"bigT1", "[", 
                RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
               RowBox[{"[", 
                RowBox[{"2", ",", "1"}], "]"}], "]"}], "*", "c0"}], "+", 
             RowBox[{
              RowBox[{"bigT1", "[", 
               RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
              RowBox[{"[", 
               RowBox[{"2", ",", "2"}], "]"}], "]"}]}], ")"}], "/", 
           RowBox[{"(", 
            RowBox[{
             RowBox[{
              RowBox[{"bigT1", "[", 
               RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
              RowBox[{"[", 
               RowBox[{"1", ",", "1"}], "]"}], "]"}], "+", 
             RowBox[{
              RowBox[{
               RowBox[{"bigT1", "[", 
                RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
               RowBox[{"[", 
                RowBox[{"1", ",", "2"}], "]"}], "]"}], "/", "c0"}], "+", 
             RowBox[{
              RowBox[{
               RowBox[{"bigT1", "[", 
                RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
               RowBox[{"[", 
                RowBox[{"2", ",", "1"}], "]"}], "]"}], "*", "c0"}], "+", 
             RowBox[{
              RowBox[{"bigT1", "[", 
               RowBox[{"f", ",", "L", ",", "Lmid", ",", "x"}], "]"}], "[", 
              RowBox[{"[", 
               RowBox[{"2", ",", "2"}], "]"}], "]"}]}], ")"}]}], "]"}], ",", 
         "2"}], "]"}]}], "]"}]}], ";"}]}]}]], "Input",
 CellChangeTimes->{{3.9204251513552046`*^9, 3.9204252111393757`*^9}, {
   3.920425304545309*^9, 3.9204253185681005`*^9}, {3.92042543464694*^9, 
   3.9204255015619516`*^9}, {3.920425543999566*^9, 3.920425583272249*^9}, {
   3.920425614750713*^9, 3.920425722975371*^9}, {3.920425855931205*^9, 
   3.9204260292915707`*^9}, {3.920426071909734*^9, 3.9204260858333387`*^9}, {
   3.9204261544453516`*^9, 3.9204261915885286`*^9}, {3.920426246946885*^9, 
   3.9204262735254707`*^9}, {3.9204263433095407`*^9, 3.920426370654233*^9}, 
   3.920426619255245*^9, {3.9204266968309383`*^9, 3.9204267301864777`*^9}, {
   3.9204267607357345`*^9, 3.9204268307593737`*^9}, {3.9204268699427805`*^9, 
   3.9204269218854237`*^9}, {3.920426954757747*^9, 3.9204269690285387`*^9}, {
   3.9204270227624598`*^9, 3.9204270364784484`*^9}, {3.9204270686115885`*^9, 
   3.920427207058223*^9}, {3.9204281815127044`*^9, 3.9204282827689767`*^9}, {
   3.920428335794609*^9, 3.92042833619915*^9}, {3.9204991336698375`*^9, 
   3.920499213064333*^9}, {3.9204993140714855`*^9, 3.9204993249343977`*^9}, {
   3.9204994055996695`*^9, 3.9204994334795303`*^9}, {3.9204994796343966`*^9, 
   3.9204995459720287`*^9}, {3.9205082192242384`*^9, 3.920508291522232*^9}, {
   3.9205112267510138`*^9, 3.920511249374179*^9}, {3.920511590747937*^9, 
   3.9205116042146482`*^9}, {3.9205120005620832`*^9, 3.920512068971851*^9}, {
   3.9205121135823402`*^9, 3.9205121203933516`*^9}, {3.9216233732258596`*^9, 
   3.9216234143671713`*^9}, {3.9216331737898655`*^9, 
   3.9216331829639473`*^9}, {3.92163322312628*^9, 3.9216332234561925`*^9}, {
   3.9228477782013426`*^9, 3.9228478334093823`*^9}, {3.922847864037857*^9, 
   3.9228478656695685`*^9}, {3.9228479252067013`*^9, 
   3.9228479937018604`*^9}, {3.92456642735633*^9, 3.9245664798932076`*^9}, {
   3.9245665100286217`*^9, 3.924566511417919*^9}, {3.9245667602240624`*^9, 
   3.9245668135553703`*^9}, {3.924566867910157*^9, 3.9245668684474163`*^9}, {
   3.9245669051269503`*^9, 3.924566915723755*^9}, {3.9245669928221097`*^9, 
   3.9245670100700073`*^9}, {3.9245670569626503`*^9, 
   3.9245670731637897`*^9}, {3.9247283695129747`*^9, 
   3.9247284345211716`*^9}, {3.925184203503868*^9, 3.925184209764169*^9}, {
   3.9252700050875187`*^9, 3.925270006561314*^9}, {3.9252704207151737`*^9, 
   3.925270424278821*^9}, 3.925270554176656*^9, {3.925270706687961*^9, 
   3.9252707235197277`*^9}, {3.925333215103709*^9, 3.9253332644979134`*^9}, {
   3.925333311137814*^9, 3.925333360340418*^9}, {3.9253478590017242`*^9, 
   3.9253480509393363`*^9}, {3.9253481344846425`*^9, 3.925348151498166*^9}, 
   3.92534818380621*^9, {3.9259350659941254`*^9, 3.9259350732558355`*^9}, {
   3.92593512566142*^9, 3.925935146526703*^9}, {3.925935184813014*^9, 
   3.925935191151675*^9}, {3.92593606646332*^9, 3.9259360959250565`*^9}, 
   3.9259361286032305`*^9, {3.9259361612546167`*^9, 3.9259362044011617`*^9}, 
   3.925936244214201*^9, {3.925936390840867*^9, 3.925936417235736*^9}, {
   3.925936470708436*^9, 3.9259365442567244`*^9}, {3.925936636210554*^9, 
   3.9259366902363424`*^9}, {3.9259367537089853`*^9, 
   3.9259368440740595`*^9}, {3.925936895229889*^9, 3.925936915960865*^9}, {
   3.925937062795593*^9, 3.925937097692995*^9}, {3.925937311069746*^9, 
   3.925937342130455*^9}, {3.925937453567978*^9, 3.925937503628279*^9}, {
   3.925937546851474*^9, 3.9259375519783287`*^9}, {3.9259375875850286`*^9, 
   3.9259375945711374`*^9}, {3.9259377775562663`*^9, 
   3.9259378147589393`*^9}, {3.9259378737917995`*^9, 3.9259379321989994`*^9}, 
   3.925938001343752*^9, 3.925940357358235*^9, {3.92594850175174*^9, 
   3.925948525568826*^9}, {3.925948559050433*^9, 3.9259486867188644`*^9}, {
   3.925948781392948*^9, 3.9259488423663497`*^9}, {3.925948884322031*^9, 
   3.925948889965188*^9}, {3.925948958211125*^9, 3.925948978912668*^9}, {
   3.9266230515653963`*^9, 3.9266230888828015`*^9}, {3.9660520997689886`*^9, 
   3.9660521320855207`*^9}},ExpressionUUID->"55ff3213-ee75-4f3a-b8b1-\
69e7ae283e02"]
},
WindowSize->{638., 499.75},
WindowMargins->{{14.25, Automatic}, {Automatic, 3.25}},
TaggingRules-><|"TryRealOnly" -> False|>,
FrontEndVersion->"13.2 for Microsoft Windows (64-bit) (2022\:5e7411\:670818\
\:65e5)",
StyleDefinitions->"Default.nb",
ExpressionUUID->"12bf9714-acbf-4e59-833d-729fa7fc08cc"
]
(* End of Notebook Content *)

(* Internal cache information *)
(*CellTagsOutline
CellTagsIndex->{}
*)
(*CellTagsIndex
CellTagsIndex->{}
*)
(*NotebookFileOutline
Notebook[{
Cell[558, 20, 14872, 344, 985, "Input",ExpressionUUID->"55ff3213-ee75-4f3a-b8b1-69e7ae283e02"]
}
]
*)

